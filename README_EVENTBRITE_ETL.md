# Eventbrite ETL Pipeline

This document describes the Eventbrite ETL (Extract, Transform, Load) pipeline that scrapes event data from Eventbrite and stores it in the database.

## Overview

The Eventbrite ETL follows this process:

1. **Get proxy IPs** from the `raw.proxy_ip` table
2. **Get Eventbrite queries** from the `raw.eventbrite_query` table  
3. **Check existing scraped event IDs** and update the event ID pool
4. **For each query**:
   - Scrape event IDs from the search URL
   - Fetch event data using the Eventbrite API
   - Process and save events and venues separately
   - Update query status

## Database Tables

### `raw.eventbrite_event`
Stores processed event data with fields like:
- `id`: Event ID
- `name`: Event name
- `start_date`, `end_date`: Event dates
- `start_time`, `end_time`: Event times
- `venue_id`: Associated venue ID
- `organizer_name`, `organizer_id`: Organizer information
- `min_ticket_price`, `max_ticket_price`: Pricing information
- `is_free`: Whether the event is free

### `raw.eventbrite_venue`
Stores venue information:
- `id`: Venue ID
- `name`: Venue name
- `address`, `city`, `state`, `country`: Location details
- `latitude`, `longitude`: Coordinates
- `timezone`: Venue timezone
- `socials`: Social media links (JSONB)

### `raw.eventbrite_query`
Stores search queries to be processed:
- `venue_id`: Unique venue identifier
- `venue_name`: Venue name
- `eventbrite_search_url`: URL to scrape for events
- `is_scraped`: Whether this query has been processed
- `retries`: Number of retry attempts
- `scraping_status_code`: HTTP status code from scraping
- `error_message`: Error details if scraping failed

### `raw.proxy_ip`
Stores proxy IP addresses:
- `id`: Unique identifier
- `ip`: Proxy IP address
- `is_blocked`: Whether the proxy is blocked

## Usage

### 1. Setup Database Tables

First, ensure the required tables exist in your database:

```sql
-- Create Eventbrite tables (see the provided SQL in the user query)
CREATE TABLE raw.eventbrite_event (...);
CREATE TABLE raw.eventbrite_venue (...);
CREATE TABLE raw.eventbrite_query (...);
CREATE TABLE raw.proxy_ip (...);
```

### 2. Populate Initial Data

Insert proxy IPs and search queries:

```sql
-- Add proxy IPs (just the IP addresses, not the full URLs)
INSERT INTO raw.proxy_ip (ip) VALUES 
('*******'),
('*******'),
('**********');

-- Add search queries
INSERT INTO raw.eventbrite_query (venue_id, venue_name, eventbrite_search_url) VALUES
('venue-1', 'Venue Name 1', 'https://www.eventbrite.com/d/search/?q=venue1'),
('venue-2', 'Venue Name 2', 'https://www.eventbrite.com/d/search/?q=venue2');
```

**Note**: The proxy IPs in the database should be just the IP addresses (e.g., `*******`). The ETL will automatically format them into full proxy URLs using the template from the config file.

### 3. Run the ETL

```python
from raw.eventbrite_etl import raw_eventbrite_job
from libs.clients.eventbrite_client import EventbriteClient
from libs.database.supabase import SupabaseDatabase

# Initialize components
eventbrite_client = EventbriteClient(
    base_api_url="https://www.eventbriteapi.com/v3/events/search/",
    proxy_url=None,  # Will be set dynamically from database
    use_stealth=True
)

database = SupabaseDatabase(
    tables=tables,
    user="your_user",
    password="your_password",
    host="your_host",
    port="5432",
    dbname="your_dbname",
    read_batch_size=100,
    write_batch_size=50
)

config = {
    "job_name": "eventbrite_raw",
    "base_api_url": "https://www.eventbriteapi.com/v3/events/search/",
    "years_to_fetch": 1,
    "proxy_url_template": "http://brd-customer-hl_57b42333-zone-datacenter_proxy1-ip-{}<EMAIL>:33335"
}

# Run the ETL job
raw_eventbrite_job(
    eventbrite_client=eventbrite_client,
    database=database,
    config=config,
    logger=logger
)
```

## Key Features

### Proxy URL Template
- Uses a template URL with `{}` placeholder for dynamic IP replacement
- Automatically formats full proxy URLs using IPs from the database
- Supports proxy rotation with proper URL formatting

### Event ID Pool Management
- Uses a pickle file to track scraped vs unscraped event IDs
- Prevents re-scraping the same events
- Maintains statistics on scraping progress

### Proxy Rotation
- Automatically rotates through available proxy IPs
- Marks blocked proxies in the database
- Provides fallback if no proxies are available

### Error Handling
- Graceful handling of API failures
- Retry logic for failed requests
- Detailed error logging and status tracking

### Data Processing
- Separates events and venues for efficient storage
- Removes duplicates based on ID
- Processes pricing and organizer information
- Handles timezone conversions

## Monitoring

### Check Query Status
```sql
SELECT venue_name, is_scraped, retries, error_message, searched_at
FROM raw.eventbrite_query
ORDER BY created_at DESC;
```

### Check Event Counts
```sql
SELECT COUNT(*) as total_events FROM raw.eventbrite_event;
SELECT COUNT(*) as total_venues FROM raw.eventbrite_venue;
```

### Check Pool Statistics
The ETL logs pool statistics showing:
- Total event IDs in pool
- Number of scraped events
- Number of unscraped events

## Configuration

### Environment Variables
Set these environment variables for database connection:
- `DB_USER`
- `DB_PASSWORD` 
- `DB_HOST`
- `DB_PORT`
- `DB_NAME`

### Table Configuration
Ensure your `tables` dictionary includes:
```python
tables = {
    "raw": {
        "eventbrite_event": "eventbrite_event",
        "eventbrite_venue": "eventbrite_venue",
        "eventbrite_query": "eventbrite_query", 
        "proxy_ip": "proxy_ip"
    }
}
```

## Troubleshooting

### Common Issues

1. **No proxy IPs available**
   - Add proxy IPs to the `raw.proxy_ip` table
   - Check if proxies are blocked

2. **No queries to process**
   - Add search queries to the `raw.eventbrite_query` table
   - Ensure URLs are valid Eventbrite search URLs

3. **API rate limiting**
   - Increase delays between requests
   - Add more proxy IPs
   - Check Eventbrite API limits

4. **Database connection issues**
   - Verify database credentials
   - Check network connectivity
   - Ensure tables exist with correct schema

### Logs
The ETL provides detailed logging with emojis for easy scanning:
- 🚀 Job start
- 📡 API requests
- ✅ Success messages
- ⚠️ Warnings
- ❌ Errors
- 📊 Statistics

## Dependencies

- `tls_client`: For web scraping
- `curl_cffi`: For API requests
- `psycopg`: For database operations
- `rich`: For enhanced logging
- `extruct`: For JSON-LD extraction
- `pickle`: For event ID pool persistence 