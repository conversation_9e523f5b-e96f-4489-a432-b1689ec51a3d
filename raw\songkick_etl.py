#--------------songkick_Start---------------
from logging import Logger
from datetime import datetime, timedelta, timezone
import random
import time
from typing import Dict, List, Optional, Any

from raw.common import (
    remove_duplicates_by_id,
    process_songkick_venues,
    SongkickUtils
)
from libs.clients.songkick_client import SongkickClient
from libs.database.database import Database


def raw_songkick_job(
        songkick_client: SongkickClient,
        database: Database,
        config: dict,
        logger: Logger
    ) -> None:
    """
    Fetches Songkick venue data by scraping venue pages, processes them,
    and writes them to the Supabase raw table.
    
    The process:
    1. Get venue URLs from sitemaps or local storage
    2. Check existing processed URLs
    3. For each unprocessed URL, scrape venue data
    4. Process and save venues to database
    
    Args:
        songkick_client (SongkickClient): An instance of the SongkickClient interface.
        database (Database): An instance of the Database interface.
        config (Dict): A dictionary containing the constants for the job from the config file.
        logger (Logger): A logger instance.
    """
    try:
        logger.info("Starting Songkick ETL job...")
        database.save_job_start_time(config["job_name"])
        
        # Get proxy URL from config
        proxy_url = config.get("proxy_url")
        if proxy_url:
            songkick_client.set_proxy_url(proxy_url)
            logger.info(f"Using proxy URL: {proxy_url}")
        
        # Initialize SongkickUtils for managing venue URLs
        sk_utils = SongkickUtils(database=database, logger=logger)
        
        # Step 1: Check if we have venue URLs, if not scrape sitemaps
        venue_urls = sk_utils.load_venue_urls()
        
        if not venue_urls:
            logger.info("No venue URLs found, scraping sitemaps...")
            sitemap_result = songkick_client.scrape_venue_sitemaps()
            
            if sitemap_result["success"]:
                venue_urls = sitemap_result["venue_urls"]
                sk_utils.save_venue_urls(venue_urls)
                logger.info(f"Scraped {len(venue_urls)} venue URLs from {sitemap_result.get('sitemap_count', 0)} sitemaps")
            else:
                logger.error(f"Failed to scrape sitemaps: {sitemap_result.get('error_message', 'Unknown error')}")
                return
        
        # Step 2: Get processing statistics
        stats = sk_utils.get_processing_stats()
        logger.info(f"Processing stats: {stats['processed']}/{stats['total']} venues processed ({stats['completion_percentage']}%)")
        
        if stats['remaining'] == 0:
            logger.info("All venues have been processed!")
            return
        
        # Step 3: Process venues in batches
        batch_size = config.get("api_size_limit", 20)
        total_venues_saved = 0
        total_batches_processed = 0
        
        while True:
            # Get next batch of unprocessed URLs
            unprocessed_urls = sk_utils.get_unprocessed_urls(limit=batch_size)
            
            if not unprocessed_urls:
                logger.info("No more unprocessed URLs found. Job completed!")
                break
            
            logger.info(f"Processing batch of {len(unprocessed_urls)} venues...")
            
            try:
                # Fetch venue data for this batch
                batch_result = songkick_client.fetch_venues_batch(unprocessed_urls, batch_size)
                
                if not batch_result["success"]:
                    logger.error(f"Batch processing failed: {batch_result.get('error_message', 'Unknown error')}")
                    # Mark URLs as processed even if failed to avoid infinite loop
                    sk_utils.mark_urls_processed(unprocessed_urls)
                    continue
                
                venues_data = batch_result["venues"]
                batch_stats = batch_result["statistics"]
                
                logger.info(f"Batch stats: {batch_stats['successful']}/{batch_stats['total_processed']} successful ({batch_stats['success_rate']:.1f}%)")
                
                if venues_data:
                    # Process venues using common function
                    processed_venues = process_songkick_venues(venues_data)
                    processed_venues = remove_duplicates_by_id(processed_venues)
                    
                    # Save to database
                    if processed_venues:
                        database.save_raw_songkick_venues(processed_venues)
                        total_venues_saved += len(processed_venues)
                        logger.info(f"Saved {len(processed_venues)} venues to database")
                
                # Mark URLs as processed
                sk_utils.mark_urls_processed(unprocessed_urls)
                total_batches_processed += 1
                
                # Add delay between batches
                delay = config.get("api_retry_delay", 5)
                time.sleep(random.uniform(delay, delay + 2))
                
                # Log progress every 5 batches
                if total_batches_processed % 5 == 0:
                    current_stats = sk_utils.get_processing_stats()
                    logger.info(f"Progress update: {current_stats['processed']}/{current_stats['total']} venues processed ({current_stats['completion_percentage']}%)")
                
            except Exception as e:
                logger.error(f"Error processing batch: {e}")
                # Mark URLs as processed to avoid infinite loop
                sk_utils.mark_urls_processed(unprocessed_urls)
                continue
        
        # Log final statistics
        final_stats = sk_utils.get_processing_stats()
        logger.info(f"Songkick ETL job completed!")
        logger.info(f"Final stats: {final_stats['processed']}/{final_stats['total']} venues processed ({final_stats['completion_percentage']}%)")
        logger.info(f"Total venues saved in this run: {total_venues_saved}")
        logger.info(f"Total batches processed: {total_batches_processed}")
        
        database.save_job_completed_time("Success")
        
    except KeyboardInterrupt:
        logger.warning("Script interrupted and stopped by user.")
        database.save_job_completed_time("Interrupted")
        
    except Exception as e:
        logger.error(f"Unhandled exception: {e}", exc_info=True)
        database.save_job_completed_time("Failed")
        raise
#--------------songkick_End---------------