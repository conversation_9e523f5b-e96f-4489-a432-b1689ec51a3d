# Songkick ETL Pipeline

This document describes the Songkick ETL (Extract, Transform, Load) pipeline that scrapes venue data from Songkick and stores it in the database.

## Overview

The Songkick ETL follows this process:

1. **Scrape venue sitemaps** from Songkick to get venue URLs
2. **Extract venue data** from individual venue pages using JSON-LD structured data
3. **Process and save venues** to the database
4. **Track processed URLs** to avoid re-scraping

## Database Tables

### `raw.songkick_venue`
Stores venue information scraped from Songkick:
- `id`: Venue ID (extracted from URL)
- `name`: Venue name
- `address`: Full address
- `city`, `state`, `country`: Location details
- `postal_code`: Postal/ZIP code
- `latitude`, `longitude`: Coordinates
- `website_url`: Venue website
- `socials`: Social media links (JSONB)
- `source_url`: Original Songkick URL
- `created_at`, `updated_at`: Timestamps

## Key Features

### Sitemap Scraping
- Automatically discovers Songkick venue sitemaps
- Supports both compressed (.gz) and uncompressed XML formats
- Extracts venue URLs using regex patterns
- Handles multiple sitemap files (venues.0.xml, venues.1.xml, etc.)

### Multi-Method Data Extraction
The client uses multiple fallback methods for robust data extraction:
1. **TLS Client**: Primary method with stealth capabilities
2. **Requests with SSL**: Standard HTTPS requests
3. **Requests without SSL**: Fallback for SSL issues
4. **No Proxy**: Attempts without proxy if proxy fails

### JSON-LD Processing
- Extracts structured data from venue pages
- Processes `MusicEvent` and `Place` schema types
- Handles address, coordinates, and social media data
- Separates website URLs from social media links

### Progress Tracking
- Maintains processed URL list in JSON files
- Provides completion statistics
- Supports resumable processing
- Batch processing with configurable sizes

## Usage

### 1. Configuration

The Songkick ETL is configured in `config/config.yaml`:

```yaml
raw_songkick:
  job_name: raw_songkick
  api_size_limit: 20          # Batch size for processing
  api_retry_limit: 3          # Number of retries
  api_retry_delay: 5          # Delay between retries (seconds)
  base_api_url: https://www.songkick.com
  proxy_url: http://your-proxy-url  # Optional proxy
```

### 2. Database Setup

Ensure the Songkick venue table exists:

```sql
CREATE TABLE raw.songkick_venue (
    id VARCHAR PRIMARY KEY,
    name VARCHAR NOT NULL,
    address TEXT,
    city VARCHAR,
    state VARCHAR,
    country VARCHAR,
    postal_code VARCHAR,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    website_url TEXT,
    socials JSONB,
    source_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Run the ETL

The Songkick ETL is integrated into the main pipeline:

```python
from raw.songkick_etl import raw_songkick_job
from libs.clients.songkick_client import SongkickClient
from libs.database.supabase import SupabaseDatabase

# Initialize components
songkick_client = SongkickClient(
    base_api_url="https://www.songkick.com",
    proxy_url="your-proxy-url",  # Optional
    use_stealth=True,
    logger=logger
)

config = {
    "job_name": "raw_songkick",
    "api_size_limit": 20,
    "api_retry_limit": 3,
    "api_retry_delay": 5,
    "base_api_url": "https://www.songkick.com",
    "proxy_url": "your-proxy-url"  # Optional
}

# Run the ETL job
raw_songkick_job(
    songkick_client=songkick_client,
    database=database,
    config=config,
    logger=logger
)
```

## Architecture

### SongkickClient
- **Sitemap Discovery**: Finds and processes venue sitemaps
- **Data Extraction**: Multiple fallback methods for robust scraping
- **Batch Processing**: Processes venues in configurable batches
- **Error Handling**: Graceful handling of network and parsing errors

### SongkickUtils
- **URL Management**: Loads and saves venue URLs
- **Progress Tracking**: Maintains processed URL lists
- **Statistics**: Provides completion and progress metrics
- **Resume Support**: Allows resuming interrupted processing

### Data Processing
- **Venue Standardization**: Converts raw data to standard format
- **Social Media Parsing**: Extracts and categorizes social links
- **Deduplication**: Removes duplicate venues by ID
- **JSONB Storage**: Stores social media data as structured JSON

## Monitoring

### Progress Tracking
```python
# Get processing statistics
stats = sk_utils.get_processing_stats()
print(f"Progress: {stats['processed']}/{stats['total']} ({stats['completion_percentage']}%)")
```

### Database Queries
```sql
-- Check venue counts
SELECT COUNT(*) as total_venues FROM raw.songkick_venue;

-- Check venues by country
SELECT country, COUNT(*) as venue_count 
FROM raw.songkick_venue 
GROUP BY country 
ORDER BY venue_count DESC;

-- Check venues with social media
SELECT COUNT(*) as venues_with_socials 
FROM raw.songkick_venue 
WHERE socials IS NOT NULL;
```

## File Structure

The ETL creates and maintains these files:
- `songkick_venue_urls.json`: All discovered venue URLs
- `songkick_processed_urls.json`: URLs that have been processed

## Error Handling

### Network Issues
- Multiple extraction methods with fallbacks
- Proxy rotation support
- Configurable retry logic
- Graceful degradation without proxy

### Data Issues
- Validates venue data before saving
- Handles missing or malformed JSON-LD
- Skips venues without required fields
- Logs detailed error information

### Processing Issues
- Maintains progress state across interruptions
- Supports resumable processing
- Batch-level error isolation
- Comprehensive logging

## Integration with Pipeline

The Songkick ETL integrates seamlessly with the existing pipeline:

1. **Raw Layer**: Scrapes and stores raw venue data
2. **Stage Layer**: Can be processed alongside other venue sources
3. **Public Layer**: Venues can be deduplicated and matched with other sources

## Performance

### Optimization Features
- Batch processing to reduce database load
- Configurable delays to respect rate limits
- Efficient URL tracking with sets
- Incremental processing support

### Typical Performance
- ~20 venues per batch (configurable)
- ~1-3 second delay between requests
- ~85-95% success rate depending on network conditions
- Processes thousands of venues per hour

## Troubleshooting

### Common Issues

1. **No venue URLs found**
   - Check sitemap availability
   - Verify network connectivity
   - Check proxy configuration

2. **Low success rate**
   - Verify proxy functionality
   - Check rate limiting
   - Review error logs for patterns

3. **Database connection issues**
   - Verify database credentials
   - Check network connectivity
   - Ensure table schema exists

## Dependencies

The Songkick ETL requires these additional packages:
- `tls-client`: For stealth web scraping
- `extruct`: For JSON-LD extraction
- `requests`: For HTTP requests
- `urllib3`: For advanced HTTP features

These are already included in the project's `requirements.txt`.