from logging import Logger

from stage.common import (
    process_eventbrite_venues
)
from libs.database.database import Database

def stage_venue_job(
        database: Database,
        config: dict,
        logger: Logger
    ) -> None:
    """
    Runs the pipeline to fetch and process raw Eventbrite venues from Supabase and writes them to the staging table.
    This function fetches the last processed date from the log table, fetches the raw Eventbrite venues
    that occurred after the last processed date, and processes them.
    It logs the progress at every step and saves the completed time with a status of "Failed" if an exception occurs.
    Args:
        database (Database): An instance of the Database interface.
        config (Dict): A dictionary containing the constants for the pipeline from the config file.
        logger (Logger): A logger instance.
    """
    try:
        logger.info("Starting stage venue ETL job...")
        last_processed_date = database.save_job_start_time(config["job_name"])
        if not last_processed_date:
            raise ValueError("Expected last_processed_date to be of type datetime, but received NoneType.")
        logger.info(f"Fetched last_processed_date: {last_processed_date}.")

        # Process and save Eventbrite venues
        # Fetch raw Eventbrite venues from table
        logger.info(f"Fetching raw Eventbrite venues...")
        eventbrite_venues = database.get_raw_eventbrite_venues(last_processed_date)
        logger.info(f"Fetched {len(eventbrite_venues)} raw Eventbrite venues.")

        if eventbrite_venues:
            # Process Eventbrite venues
            logger.info(f"Processing {len(eventbrite_venues)} Eventbrite venues ...")
            processed_eventbrite_venues = process_eventbrite_venues(eventbrite_venues)
            logger.info("Venues processed.")

            if processed_eventbrite_venues:
                # Save Eventbrite venues to Supabase stage table
                logger.info(f"Saving {len(processed_eventbrite_venues)} Eventbrite venues to stage schema...")
                database.save_stage_venues(processed_eventbrite_venues)
                logger.info(f"Venues saved.")

        else:
            logger.info("No Eventbrite venues to save.")

        # Update last_processed_date in log table
        database.update_last_processed_date()
        database.save_job_completed_time("Success")
        logger.info("Stage venue ETL job completed.")

    except KeyboardInterrupt:
        logger.critical("Script interrupted and stopped by user.")
        database.save_job_completed_time("Interrupted")

    except Exception:
        logger.error("Unhandled exception", exc_info=True)
        database.save_job_completed_time("Failed")