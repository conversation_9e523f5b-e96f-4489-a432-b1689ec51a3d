#--------------songkick_Start---------------
import tls_client
import extruct
import json
import time
import random
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import urllib3
from datetime import datetime
from typing import Dict, List, Optional, Any
from logging import Logger

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class SongkickClient:
    """
    Client for scraping Songkick venue data using multiple fallback methods.
    """
    
    def __init__(self, base_api_url: str = None, proxy_url: str = None, use_stealth: bool = True, logger: Logger = None):
        """
        Initialize the Songkick client.
        
        Args:
            base_api_url: Base URL for Songkick API (not used for scraping)
            proxy_url: Proxy URL for requests
            use_stealth: Whether to use stealth mode
            logger: Logger instance
        """
        self.base_api_url = base_api_url or "https://www.songkick.com"
        self.proxy_url = proxy_url
        self.use_stealth = use_stealth
        self.logger = logger
        
        # Standard headers for requests
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }
        
        # Initialize session
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        if self.logger:
            self.logger.info(f"Songkick client initialized with stealth: {use_stealth}")
    
    def set_proxy_url(self, proxy_url: str) -> None:
        """Set the proxy URL for requests."""
        self.proxy_url = proxy_url
        if self.logger:
            self.logger.info(f"Proxy URL updated: {proxy_url}")
    
    def scrape_venue_sitemaps(self) -> Dict[str, Any]:
        """
        Scrape Songkick venue sitemaps to get venue URLs.
        
        Returns:
            Dict containing success status, venue URLs, and any error messages
        """
        try:
            if self.logger:
                self.logger.info("Looking for Songkick venue sitemaps...")
            
            venue_sitemap_urls = []
            
            # Try common Songkick venue sitemap patterns
            base_patterns = [
                "https://www.songkick.com/sitemap/venues.{}.xml.gz",
                "https://www.songkick.com/sitemap/venues.{}.xml"
            ]
            
            # Test for multiple numbered sitemaps (0-20)
            for i in range(21):
                for pattern in base_patterns:
                    test_url = pattern.format(i)
                    try:
                        response = self.session.head(test_url)
                        if response.status_code == 200:
                            venue_sitemap_urls.append(test_url)
                            if self.logger:
                                self.logger.info(f"Found venue sitemap: {test_url}")
                            break
                    except:
                        continue
                
                time.sleep(0.3)  # Be respectful
            
            if not venue_sitemap_urls:
                return {
                    "success": False,
                    "error_message": "No venue sitemaps found",
                    "venue_urls": []
                }
            
            # Process sitemaps and extract venue URLs
            venue_urls = self._process_sitemap_content(venue_sitemap_urls)
            
            return {
                "success": True,
                "venue_urls": venue_urls,
                "sitemap_count": len(venue_sitemap_urls)
            }
            
        except Exception as e:
            error_msg = f"Error scraping venue sitemaps: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            
            return {
                "success": False,
                "error_message": error_msg,
                "venue_urls": []
            }
    
    def _process_sitemap_content(self, sitemap_urls: List[str]) -> List[str]:
        """Process sitemap content and extract venue URLs."""
        import gzip
        import re
        
        all_venue_urls = []
        
        for i, url in enumerate(sitemap_urls):
            try:
                if self.logger:
                    self.logger.info(f"Processing sitemap {i+1}/{len(sitemap_urls)}: {url}")
                
                response = self.session.get(url)
                response.raise_for_status()
                
                # Process content based on file type
                if url.endswith('.gz'):
                    content = gzip.decompress(response.content).decode('utf-8')
                else:
                    content = response.text
                
                # Extract venue URLs using regex
                url_pattern = r'<loc>(https?://[^<]+)</loc>'
                found_urls = re.findall(url_pattern, content)
                
                for url in found_urls:
                    if self._is_venue_url(url):
                        all_venue_urls.append(url)
                
                if self.logger:
                    self.logger.info(f"Found {len(all_venue_urls)} venue URLs so far...")
                
                time.sleep(1)  # Be respectful
                
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"Error processing {url}: {e}")
        
        return all_venue_urls
    
    def _is_venue_url(self, url: str) -> bool:
        """Check if URL is venue-related - specifically for Songkick venue pattern."""
        import re
        # Songkick venue URL pattern: https://www.songkick.com/venues/[ID]-[venue-name]
        venue_pattern = r'https://www\.songkick\.com/venues/\d+-'
        return bool(re.match(venue_pattern, url))
    
    def extract_venue_data(self, url: str, delay: bool = True) -> Dict[str, Any]:
        """
        Extract venue data from a Songkick venue URL using multiple fallback methods.
        
        Args:
            url: Venue URL to scrape
            delay: Whether to add delay between requests
            
        Returns:
            Dict containing success status, venue data, and any error messages
        """
        try:
            if delay:
                sleep_time = random.uniform(1, 3)
                time.sleep(sleep_time)
            
            if self.logger:
                self.logger.info(f"Extracting venue data from: {url}")
            
            # Try multiple methods to extract data
            structured_data = self._extract_jsonld_data(url)
            
            if not structured_data:
                return {
                    "success": False,
                    "error_message": "Failed to extract structured data",
                    "venue_data": None
                }
            
            # Process the structured data to extract venue information
            venues = self._collect_venue_data_from_schema(structured_data, url)
            
            if not venues:
                return {
                    "success": False,
                    "error_message": "No venue data found in schema",
                    "venue_data": None
                }
            
            return {
                "success": True,
                "venue_data": venues[0] if venues else None,  # Return first venue
                "all_venues": venues
            }
            
        except Exception as e:
            error_msg = f"Error extracting venue data from {url}: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            
            return {
                "success": False,
                "error_message": error_msg,
                "venue_data": None
            }
    
    def _extract_jsonld_data(self, url: str) -> Optional[Dict]:
        """Extract JSON-LD structured data from URL with multiple fallback methods."""
        
        # Method 1: Try tls_client first
        try:
            session = tls_client.Session(
                client_identifier="chrome120",
                random_tls_extension_order=True
            )
            
            if self.proxy_url:
                session.proxies = {
                    'http': self.proxy_url,
                    'https': self.proxy_url
                }
            
            response = session.get(url, headers=self.headers, timeout_seconds=30)
            
            if response.status_code == 200:
                data = extruct.extract(
                    response.text,
                    base_url=url,
                    syntaxes=['json-ld', 'microdata', 'rdfa']
                )
                return data
                    
        except Exception:
            pass
        
        # Method 2: Try requests with SSL verification
        try:
            requests_session = requests.Session()
            
            if self.proxy_url:
                requests_session.proxies = {
                    'http': self.proxy_url,
                    'https': self.proxy_url
                }
            
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )
            
            adapter = HTTPAdapter(max_retries=retry_strategy)
            requests_session.mount("http://", adapter)
            requests_session.mount("https://", adapter)
            
            response = requests_session.get(
                url, 
                headers=self.headers, 
                timeout=30,
                verify=True
            )
            
            response.raise_for_status()
            
            data = extruct.extract(
                response.text,
                base_url=url,
                syntaxes=['json-ld', 'microdata', 'rdfa']
            )
            
            return data
            
        except Exception:
            pass
        
        # Method 3: Try requests without SSL verification
        try:
            requests_session = requests.Session()
            
            if self.proxy_url:
                requests_session.proxies = {
                    'http': self.proxy_url,
                    'https': self.proxy_url
                }
            
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )
            
            adapter = HTTPAdapter(max_retries=retry_strategy)
            requests_session.mount("http://", adapter)
            requests_session.mount("https://", adapter)
            
            response = requests_session.get(
                url, 
                headers=self.headers, 
                timeout=30,
                verify=False
            )
            
            response.raise_for_status()
            
            data = extruct.extract(
                response.text,
                base_url=url,
                syntaxes=['json-ld', 'microdata', 'rdfa']
            )
            
            return data
            
        except Exception:
            pass
        
        # Method 4: Try without proxy
        if self.proxy_url:
            try:
                requests_session = requests.Session()
                
                retry_strategy = Retry(
                    total=2,
                    backoff_factor=1,
                    status_forcelist=[429, 500, 502, 503, 504],
                )
                
                adapter = HTTPAdapter(max_retries=retry_strategy)
                requests_session.mount("http://", adapter)
                requests_session.mount("https://", adapter)
                
                response = requests_session.get(
                    url, 
                    headers=self.headers, 
                    timeout=30,
                    verify=False
                )
                
                response.raise_for_status()
                
                data = extruct.extract(
                    response.text,
                    base_url=url,
                    syntaxes=['json-ld', 'microdata', 'rdfa']
                )
                
                return data
                
            except Exception:
                pass
        
        if self.logger:
            self.logger.warning(f"All methods failed for: {url}")
        
        return None
    
    def _collect_venue_data_from_schema(self, structured_data: Dict, source_url: str) -> List[Dict]:
        """Collect venue data from JSON-LD schema."""
        venues_data = []
        processed_venues = set()
        
        if not structured_data or 'json-ld' not in structured_data:
            return venues_data
        
        json_ld_data = structured_data['json-ld']
        
        for item in json_ld_data:
            location = None
            
            if item.get('@type') == 'MusicEvent':
                location = item.get('location', {})
            elif item.get('@type') == 'Place':
                location = item
            
            if location and location.get('@type') == 'Place':
                
                venue_name = location.get('name')
                if not venue_name:
                    continue
                    
                venue_unique_id = f"{venue_name}_{source_url}"
                if venue_unique_id in processed_venues:
                    continue
                processed_venues.add(venue_unique_id)
                
                # Process address data
                address_data = location.get('address', {})
                formatted_address = {}
                full_address = ""
                
                if isinstance(address_data, dict):
                    formatted_address = {
                        'street_address': address_data.get('streetAddress'),
                        'city': address_data.get('addressLocality'),
                        'state': address_data.get('addressRegion'),
                        'postal_code': address_data.get('postalCode'),
                        'country': address_data.get('addressCountry')
                    }
                    
                    address_parts = [
                        formatted_address.get('street_address'),
                        formatted_address.get('city'),
                        formatted_address.get('state'),
                        formatted_address.get('postal_code'),
                        formatted_address.get('country')
                    ]
                    full_address = ', '.join([part for part in address_parts if part])
                
                # Process geographic coordinates
                geo_data = location.get('geo', {})
                coordinates = {}
                if geo_data and geo_data.get('@type') == 'GeoCoordinates':
                    coordinates = {
                        'latitude': geo_data.get('latitude'),
                        'longitude': geo_data.get('longitude')
                    }
                
                # Process website and social media links
                website_url = location.get('url')
                social_media_links = []
                same_as = location.get('sameAs')
                
                if same_as:
                    if isinstance(same_as, list):
                        social_media_links.extend(same_as)
                    else:
                        social_media_links.append(same_as)
                
                if website_url and social_media_links:
                    social_media_links = [link for link in social_media_links if link != website_url]
                
                final_social_media = []
                final_website_url = website_url
                
                for link in social_media_links:
                    social_domains = [
                        'facebook.com', 'twitter.com', 'instagram.com', 'youtube.com',
                        'linkedin.com', 'tiktok.com', 'snapchat.com', 'pinterest.com'
                    ]
                    
                    is_social = any(domain in link.lower() for domain in social_domains)
                    
                    if is_social:
                        final_social_media.append(link)
                    else:
                        if not final_website_url:
                            final_website_url = link
                        else:
                            final_social_media.append(link)
                
                # Build venue data
                venue_data = {
                    'id': source_url.split('/')[-1] if source_url else None,
                    'name': venue_name,
                    'address': full_address,
                    'street_address': formatted_address.get('street_address'),
                    'city': formatted_address.get('city'),
                    'state': formatted_address.get('state'),
                    'postal_code': formatted_address.get('postal_code'),
                    'country': formatted_address.get('country'),
                    'latitude': coordinates.get('latitude'),
                    'longitude': coordinates.get('longitude'),
                    'website_url': final_website_url,
                    'social_media_links': final_social_media if final_social_media else None,
                    'source_url': source_url,
                    'extraction_timestamp': datetime.now().isoformat()
                }
                
                # Only add if venue has a name
                if venue_data['name']:
                    venues_data.append(venue_data)
        
        return venues_data
    
    def fetch_venues_batch(self, venue_urls: List[str], batch_size: int = 10) -> Dict[str, Any]:
        """
        Fetch venue data for a batch of URLs.
        
        Args:
            venue_urls: List of venue URLs to process
            batch_size: Number of venues to process in this batch
            
        Returns:
            Dict containing success status, venue data, and statistics
        """
        try:
            if self.logger:
                self.logger.info(f"Processing batch of {min(batch_size, len(venue_urls))} venues")
            
            venues_data = []
            successful_extractions = 0
            failed_extractions = 0
            
            for i, url in enumerate(venue_urls[:batch_size]):
                try:
                    if self.logger:
                        self.logger.info(f"Processing venue {i+1}/{min(batch_size, len(venue_urls))}: {url}")
                    
                    result = self.extract_venue_data(url)
                    
                    if result["success"] and result["venue_data"]:
                        venues_data.append(result["venue_data"])
                        successful_extractions += 1
                        
                        if self.logger:
                            self.logger.info(f"Found venue: {result['venue_data']['name']}")
                    else:
                        failed_extractions += 1
                        if self.logger:
                            self.logger.warning(f"Failed to extract venue data: {result.get('error_message', 'Unknown error')}")
                
                except Exception as e:
                    failed_extractions += 1
                    if self.logger:
                        self.logger.error(f"Error processing {url}: {str(e)}")
            
            return {
                "success": True,
                "venues": venues_data,
                "statistics": {
                    "total_processed": min(batch_size, len(venue_urls)),
                    "successful": successful_extractions,
                    "failed": failed_extractions,
                    "success_rate": (successful_extractions / min(batch_size, len(venue_urls))) * 100 if min(batch_size, len(venue_urls)) > 0 else 0
                }
            }
            
        except Exception as e:
            error_msg = f"Error processing venue batch: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            
            return {
                "success": False,
                "error_message": error_msg,
                "venues": [],
                "statistics": {
                    "total_processed": 0,
                    "successful": 0,
                    "failed": len(venue_urls[:batch_size]),
                    "success_rate": 0
                }
            }
#--------------songkick_End---------------