import tls_client
import os
import time
import logging
from extruct.jsonld import JsonLdExtractor
from rich.logging import RichHandler
import json
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import random
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from raw.common import EventbriteUtils


class EventbriteClient:
    """A consolidated class to scrape Eventbrite event IDs and fetch event data"""
    
    def __init__(self, base_api_url: str, proxy_url: Optional[str] = None, use_stealth: bool = True):
        """
        Initialize the Eventbrite ETL scraper
        
        Args:
            base_api_url: Base URL for Eventbrite API
            proxy_url: Optional proxy URL for requests (can be template with {} placeholder)
            use_stealth: Whether to use stealth browser settings
        """
        # Set up logging with Rich
        FORMAT = "%(message)s"
        logging.basicConfig(
            level="INFO", 
            format=FORMAT, 
            datefmt="[%X]", 
            handlers=[<PERSON><PERSON><PERSON><PERSON>(rich_tracebacks=True)]
        )
        self.log = logging.getLogger("rich")
        self.console = Console()
        
        # Initialize JSON-LD extractor
        self.jsde = JsonLdExtractor()
        
        # API configuration for event fetching
        self.base_url = base_api_url
        self.proxy_url = proxy_url
        self.use_stealth = use_stealth
        
        # Initialize TLS client session for ID scraping
        self.session = self._create_session(proxy_url, use_stealth)
        
        # Default headers for API requests
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
            'Accept': 'application/json',
        }
        
        # Default parameters for API requests
        self.default_params = {
            "expand": "primary_venue,ticket_availability,primary_organizer",
            "page_size": 50,
            "include_parent_events": "true"
        }
        
        self.log.info("EventbriteETL initialized successfully")
    
    def set_proxy_url(self, proxy_url: str):
        """Set a new proxy URL for the client and update both sessions"""
        self.proxy_url = proxy_url
        self.log.info(f"Setting proxy URL: {proxy_url}")
        
        # Recreate the TLS session with new proxy
        self.session = self._create_session(proxy_url, self.use_stealth)
    
    def _create_session(self, proxy_url: Optional[str], use_stealth: bool) -> tls_client.Session:
        """Create and configure the TLS client session for ID scraping"""
        if use_stealth:
            session = tls_client.Session(
                client_identifier="chrome_112",
                random_tls_extension_order=True
            )
        else:
            session = tls_client.Session()
        
        # Set proxy if provided
        if proxy_url:
            session.proxies.update({
                "http": proxy_url,
                "https": proxy_url
            })
            self.log.debug(f"TLS session configured with proxy: {proxy_url}")
        
        # Set browser-like headers
        session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                    "(KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
             "Accept-Language": "en-US,en;q=0.9",
              "Referer": "https://www.google.com/",
             "Upgrade-Insecure-Requests": "1",
             "DNT": "1"
        })
        
        return session
    
    def parse_event_data(self, events: List[Dict[str, Any]]) -> List[str]:
        """Parse the event data and return a list of event IDs"""
        event_ids = []
        for event in events:
            item = event.get("item", {})
            if item:
                event_id = item.get("url", "").rsplit("-")[-1]
                if event_id:
                    event_ids.append(event_id)

        return event_ids
    
    def scrape_event_ids(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape an Eventbrite event page and extract event IDs"""
        try:
            self.log.info(f"Making request to: {url}")
            if self.proxy_url:
                self.log.info(f"Using proxy for scraping: {self.proxy_url}")
            
            response = self.session.get(url)
            
            self.log.info(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                self.log.info("Extracting JSON-LD data from response")
                events = self.jsde.extract(response.text)
                
                if events and len(events) > 0:
                    self.log.info(f"Found {len(events)} JSON-LD event blocks")
                    event_ids = self.parse_event_data(events[0]["itemListElement"])
                    self.log.info(f"Parsed {len(event_ids)} event IDs: {event_ids[:5]}...")  # Show first 5
                    return {"success": True, "event_ids": event_ids}
                else:
                    self.log.warning("No JSON-LD events found in response")
                    return {"success": True, "event_ids": []}
            else:
                self.log.error(f"HTTP {response.status_code}: {response.text[:200]}...")  # Show first 200 chars
                return {
                    "success": False, 
                    "error_code": response.status_code,
                    "error_message": response.text[:200],
                    "event_ids": []
                }
        except Exception as e:
            self.log.error(f"Exception during scraping: {e}")
            return {
                "success": False,
                "error_code": 0,
                "error_message": str(e),
                "event_ids": []
            }
    
    def fetch_events(self, event_ids: List[str]) -> Optional[Dict[str, Any]]:
        """
        Fetch event data from Eventbrite API using a list of event IDs
        
        Args:
            event_ids: List of Eventbrite event IDs
            
        Returns:
            Parsed JSON response with event data, or error dict with status_code and error_message
        """
        try:
            # Prepare parameters
            params = self.default_params.copy()
            params["event_ids"] = ",".join(event_ids)
            
            # Make request with progress indicator
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
                transient=True
            ) as progress:
                task = progress.add_task("Making API request...", total=None)
                
                # Use the same TLS session for consistency
                response = self.session.get(
                    self.base_url,
                    headers=self.headers,
                    params=params
                )
                
                progress.update(task, description="Processing response...")
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                # Return error information for proper handling
                return {
                    "error": True,
                    "status_code": response.status_code,
                    "error_message": response.text,
                    "events": []
                }
                
        except Exception as e:
            # Return error information for proper handling
            return {
                "error": True,
                "status_code": 0,
                "error_message": str(e),
                "events": []
            }
    
    def process_eventbrite_url(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Complete ETL process: scrape event IDs and fetch event data
        
        Args:
            url: Eventbrite search URL
            
        Returns:
            Event data dictionary or None on failure
        """
        try:
            # Step 1: Scrape event IDs
            event_ids = self.scrape_event_ids(url)
            if not event_ids:
                self.log.error("Failed to scrape event IDs")
                return None
            
            # Step 2: Fetch event data
            event_data = self.fetch_events(event_ids)
            if not event_data:
                self.log.error("Failed to fetch event data")
                return None
            
            self.log.info(f"ETL process completed successfully for {len(event_ids)} events")
            return event_data
            
        except Exception as e:
            self.log.error(f"Error in ETL process: {e}")
            return None 