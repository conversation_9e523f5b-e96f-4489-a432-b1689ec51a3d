from logging import Logger
from datetime import datetime, timedelta, timezone
import random
import time

from raw.common import (
    EventbriteUtils,
    process_eventbrite_events,
    process_eventbrite_venues,
    remove_duplicates_by_id
)
from libs.clients.eventbrite_client import EventbriteClient
from libs.database.database import Database


def raw_eventbrite_job(
        eventbrite_client: EventbriteClient,
        database: Database,
        config: dict,
        logger: Logger
    ) -> None:
    """
    Fetches Eventbrite event data from Eventbrite API, processes them,
    and writes them to the Supabase raw table.
    
    The process:
    1. Get Eventbrite queries from local storage
    2. Check existing scraped event IDs and update pool
    3. For each query, scrape event IDs and fetch event data using the same session
    4. Process and save events and venues separately
    
    Args:
        eventbrite_client (EventbriteClient): An instance of the EventbriteClient interface.
        database (Database): An instance of the Database interface.
        config (Dict): A dictionary containing the constants for the job from the config file.
        logger (Logger): A logger instance.
    """
    try:
        logger.info("Starting Eventbrite ETL job...")
        database.save_job_start_time(config["job_name"])
        
        # Get proxy URL from config
        proxy_url = config.get("proxy_url")
        if not proxy_url:
            logger.error("No proxy URL provided in config. Cannot proceed.")
            return
        
        # Set proxy URL for the client
        # eventbrite_client.set_proxy_url(proxy_url)
        # logger.info(f"Using proxy URL: {proxy_url}")
        
        # Initialize EventbriteUtils for managing event ID pool (no proxy management)
        eb_utils = EventbriteUtils(database=database)
        
        # Check if event pool file exists and log status
        import os
        pool_file = "event_id_pool.pkl"
        if os.path.exists(pool_file):
            logger.info(f"Event pool file found: {pool_file}")
        else:
            logger.info(f"Event pool file created: {pool_file}")
        
        # Log initial pool stats
        initial_stats = eb_utils.get_pool_stats()
        logger.info(f"Initial pool stats: {initial_stats}")
        
        # Step 1: Get Eventbrite queries from local storage
        queries = eb_utils.get_queries()
        if not queries:
            new_queries = eb_utils.generate_more_queries()
            if new_queries > 0:
                queries = eb_utils.get_queries()
                logger.info(f"Generated {new_queries} initial queries")
            else:
                logger.warning("No queries could be generated")
                return
        
        logger.info(f"Processing {len(queries)} queries")
        
        # If we have very few queries, try to generate more immediately
        if len(queries) < 20:
            logger.info(f"Only {len(queries)} queries available, generating more...")
            new_queries = eb_utils.generate_more_queries()
            if new_queries > 0:
                queries = eb_utils.get_queries()
                logger.info(f"Generated {new_queries} additional queries, total now: {len(queries)}")
        
        # Step 2: Process queries continuously
        processed_venues = 0
        total_events = 0
        total_venues = 0
        queries_with_events = 0
        queries_without_events = 0
        query_index = 0
        
        while True:
            # Get current queries (they may have been updated)
            current_queries = eb_utils.get_queries()
            
            # If we've processed all current queries, try to generate more
            if query_index >= len(current_queries):
                logger.info("Processed all current queries, generating more from venue database...")
                new_queries = eb_utils.generate_more_queries()
                if new_queries > 0:
                    current_queries = eb_utils.get_queries()
                    logger.info(f"Generated {new_queries} new queries, total now: {len(current_queries)}")
                else:
                    logger.info("No more venues available in database. ETL job completed.")
                    break
            
            # Get the next query to process
            query = current_queries[query_index]
            query_index += 1
            
            eventbrite_client.set_proxy_url(proxy_url)
            logger.info(f"Using proxy URL: {proxy_url}")
            venue_id = query.get("venue_id")
            venue_name = query.get("venue_name", "Unknown")
            search_url = query.get("eventbrite_search_url")
            
            if not search_url:
                logger.warning(f"Skipping query {query_index}: No search URL for venue {venue_name}")
                continue
            
            logger.info(f"Processing query {query_index}/{len(current_queries)}: {venue_name} ({venue_id})")
            
            try:
                # Scrape event IDs using the same session
                logger.info(f"Scraping event IDs from: {search_url}")
                scrape_result = eventbrite_client.scrape_event_ids(search_url)
                
                if not scrape_result or not scrape_result.get("success"):
                    error_message = scrape_result.get("error_message") if scrape_result else "Unknown error"
                    logger.warning(f"Scraping failed for venue {venue_name}: {error_message}")
                    queries_without_events += 1
                    continue
                
                event_ids = scrape_result.get("event_ids", [])
                if not event_ids:
                    logger.warning(f"No event IDs found for venue {venue_name}.")
                    queries_without_events += 1
                    continue
                
                logger.info(f"Found {len(event_ids)} event IDs for venue {venue_name}")
                queries_with_events += 1
                
                # Add to pool and get unscraped
                added_count = eb_utils.add_event_ids(event_ids)
                logger.info(f"Added {added_count} new event IDs to pool for venue {venue_name}")
                
                unscraped_ids = eb_utils.get_unscraped_ids(limit=20)
                logger.info(f"Retrieved {len(unscraped_ids)} unscraped IDs from pool")
                
                if not unscraped_ids:
                    logger.info(f"No unscraped IDs available for venue {venue_name}, continuing to next query")
                    continue
                
                # Fetch event data using the same session
                logger.info(f"Fetching event data for {len(unscraped_ids)} events")
                event_data = eventbrite_client.fetch_events(unscraped_ids)
                
                if not event_data or event_data.get("error"):
                    error_message = event_data.get("error_message") if event_data else "Unknown error"
                    logger.warning(f"Failed to fetch event data for venue {venue_name}. Error: {error_message}")
                    continue
                
                events = event_data["events"]
                if not events:
                    logger.warning(f"No events returned in API response for venue {venue_name}")
                    continue
                
                logger.info(f"Successfully fetched {len(events)} events for venue {venue_name}")
                
                # Process events and venues
                processed_events = process_eventbrite_events(events)
                processed_events = remove_duplicates_by_id(processed_events)
                
                # Extract venues
                processed_venues_list = []
                venue_ids = set()
                
                for event in events:
                    if not event:
                        continue
                        
                    venue = event.get("primary_venue")
                    organizer = event.get("primary_organizer", {}) or {}
                    
                    if venue and venue.get("id") and venue.get("id") not in venue_ids:
                        organizer_name = organizer.get("name")
                        venue_name = venue.get("name")
                        
                        if organizer_name and venue_name and organizer_name.lower() == venue_name.lower():
                            venue["organizer_website"] = organizer.get("website_url")
                            venue["organizer_facebook"] = organizer.get("facebook")
                            venue["organizer_twitter"] = organizer.get("twitter")
                        
                        if not venue.get("timezone") and event.get("timezone"):
                            venue["timezone"] = event.get("timezone")
                        
                        processed_venues_list.append(venue)
                        venue_ids.add(venue.get("id"))
                
                processed_venues_list = process_eventbrite_venues(processed_venues_list)
                processed_venues_list = remove_duplicates_by_id(processed_venues_list)
                
                # Save to database
                if processed_events:
                    database.save_raw_eventbrite_events(processed_events)
                    total_events += len(processed_events)
                    logger.info(f"Saved {len(processed_events)} events to database for venue {venue_name}")
                
                if processed_venues_list:
                    database.save_raw_eventbrite_venues(processed_venues_list)
                    total_venues += len(processed_venues_list)
                    logger.info(f"Saved {len(processed_venues_list)} venues to database for venue {venue_name}")
                
                # Mark as scraped
                marked_count = eb_utils.mark_scraped(unscraped_ids)
                logger.info(f"Marked {marked_count} events as scraped for venue {venue_name}")
                processed_venues += 1
                
                # Add delay between requests
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                logger.error(f"Error processing venue {venue_name} ({venue_id}): {e}")
                continue
        
        # Log final statistics
        final_stats = eb_utils.get_pool_stats()
        logger.info(f"Completed: {processed_venues} venues processed, {total_events} events saved, {total_venues} venues saved")
        logger.info(f"Queries with events: {queries_with_events}, Queries without events: {queries_without_events}")
        logger.info(f"Final pool stats: {final_stats}")
        logger.info(f"Pool growth: {final_stats['total'] - initial_stats['total']} new events added")
        
        logger.info("Eventbrite ETL job completed successfully")
        database.save_job_completed_time("Success")
        
    except KeyboardInterrupt:
        logger.warning("Script interrupted and stopped by user.")
        database.save_job_completed_time("Interrupted")
        
    except Exception as e:
        logger.error(f"Unhandled exception: {e}", exc_info=True)
        database.save_job_completed_time("Failed")
        raise
