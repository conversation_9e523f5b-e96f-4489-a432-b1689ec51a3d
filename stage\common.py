import json
from typing import Optional
from datetime import datetime, timedelta
import pytz


def process_seatgeek_artists(
    seatgeek_artists: list[dict]
) -> list[dict]:
    """
    Process artists from Seatgeek.

    :param seatgeek_artists: List of dictionaries containing Ticketmaster data.
    :return: List of dictionaries containing processed data.
    """
    processed_seatgeek_artists = []

    # Add Ticketmaster data
    for sg_entry in seatgeek_artists:
        processed_seatgeek_artists.append({
            'name': sg_entry.get('name'),
            'spotify_id': sg_entry.get('spotify_id'),
            'image': sg_entry.get('image'),
            'source': 'seatgeek',
            'source_id': sg_entry.get('id')
        })

    return processed_seatgeek_artists


def process_ticketmaster_artists(
    ticketmaster_artists: list[dict]
) -> list[dict]:
    """
    Process artists from Ticketmaster.

    :param ticketmaster_artists: List of dictionaries containing Ticketmaster data.
    :return: List of dictionaries containing processed data.
    """
    processed_ticketmaster_artists = []

    # Add Ticketmaster data
    for tm_entry in ticketmaster_artists:
        processed_ticketmaster_artists.append({
            'name': tm_entry.get('name'),
            'spotify_id': tm_entry.get('spotify_id'),
            'image': __get_best_image_url(tm_entry),
            'source': 'ticketmaster',
            'source_id': tm_entry.get('id')
        })

    return processed_ticketmaster_artists



def __convert_eventbrite_datetime(start_time: str, timezone_str: str) -> tuple[Optional[datetime], Optional[datetime]]:
    """
    Convert Eventbrite start time to UTC and local datetime objects.
    
    Args:
        start_time: Eventbrite start time string (e.g., "2024-01-15T19:00:00Z")
        timezone_str: Event timezone string (e.g., "America/New_York")
    
    Returns:
        tuple: (datetime_local, datetime_utc) as datetime objects
    """
    try:
        # Parse the start time (Eventbrite provides UTC time)
        utc_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        
        # Convert to the event's timezone for local time
        if timezone_str:
            try:
                event_tz = pytz.timezone(timezone_str)
                local_dt = utc_dt.astimezone(event_tz)
                return local_dt, utc_dt
            except pytz.exceptions.UnknownTimeZoneError:
                # If timezone is invalid, use UTC for both
                return utc_dt, utc_dt
        else:
            # If no timezone provided, use UTC for both
            return utc_dt, utc_dt
            
    except (ValueError, TypeError):
        # If parsing fails, return None for both
        return None, None


def __convert_eventbrite_datetime_from_fields(start_date, start_time, timezone_str: str) -> tuple[Optional[datetime], Optional[datetime]]:
    """
    Convert Eventbrite start date and time to UTC and local datetime objects.
    
    Args:
        start_date: Eventbrite start date (date object)
        start_time: Eventbrite start time (time object)
        timezone_str: Event timezone string (e.g., "America/New_York")
    
    Returns:
        tuple: (datetime_local, datetime_utc) as datetime objects
    """
    try:
        if not start_date or not start_time:
            return None, None
            
        # Combine date and time
        local_dt = datetime.combine(start_date, start_time)
        
        # Convert to the event's timezone
        if timezone_str:
            try:
                event_tz = pytz.timezone(timezone_str)
                local_dt = event_tz.localize(local_dt)
                utc_dt = local_dt.astimezone(pytz.UTC)
                return local_dt, utc_dt
            except pytz.exceptions.UnknownTimeZoneError:
                # If timezone is invalid, assume local timezone
                utc_dt = local_dt.replace(tzinfo=pytz.UTC)
                return local_dt, utc_dt
        else:
            # If no timezone provided, assume local timezone
            utc_dt = local_dt.replace(tzinfo=pytz.UTC)
            return local_dt, utc_dt
            
    except (ValueError, TypeError):
        # If parsing fails, return None for both
        return None, None


def process_seatgeek_venues(seatgeek_venues: list[dict]) -> list[dict]:
    processed_venues = []

    for venue in seatgeek_venues:
        processed_venues.append({
            "name": venue.get("name", None),
            "address": venue.get("address", None),
            "city": venue.get("city", None),
            "state": venue.get("state", None),
            "country": venue.get("country", None),
            "postal_code": venue.get("postal_code", None),
            "timezone": venue.get("timezone", None),
            "latitude": str(venue.get("location", {}).get("lat", None)),
            "longitude": str(venue.get("location", {}).get("lon", None)),
            "capacity": venue.get("capacity", None),
            "source": "seatgeek",
            "source_id": venue.get("id")
        })

    return processed_venues


def process_ticketmaster_venues(ticketmaster_venues: list[dict]) -> list[dict]:
    processed_venues = []

    for venue in ticketmaster_venues:
        processed_venues.append({
            "name": venue.get("name", None),
            "address": venue.get("address", None),
            "city": venue.get("city", None),
            "state": venue.get("state", None),
            "country": venue.get("country", None),
            "postal_code": venue.get("postal_code", None),
            "timezone": venue.get("timezone", None),
            "latitude": venue.get("location", {}).get("latitude", None),
            "longitude": venue.get("location", {}).get("longitude", None),
            "capacity": 0,
            "source": "ticketmaster",
            "source_id": venue.get("id")
        })

    return processed_venues


def process_eventbrite_venues(eventbrite_venues: list[dict]) -> list[dict]:
    processed_venues = []

    for venue in eventbrite_venues:
        processed_venues.append({
            "name": venue.get("name", None),
            "address": venue.get("address", None),
            "city": venue.get("city", None),
            "state": venue.get("state", None),
            "country": venue.get("country", None),
            "postal_code": venue.get("postal_code", None),
            "timezone": venue.get("timezone", None),
            "latitude": venue.get("latitude", None),
            "longitude": venue.get("longitude", None),
            "capacity": None,  # Eventbrite doesn't provide capacity
            "phone": None,     # Eventbrite doesn't provide phone
            "website": venue.get("url", None),  # Eventbrite uses 'url' field
            "social": venue.get("socials", None),  # Eventbrite uses 'socials' field
            "source": "eventbrite",
            "source_id": venue.get("id")
        })

    return processed_venues


def process_seatgeek_events(events: list[dict]) -> list[dict]:
    processed_events = []
    for event in events:
        type = __process_seatgeek_types(event.get("type", None))
        processed_events.append({
            "name": event.get("title", None),
            "datetime_local": event.get("datetime_local", None),
            "datetime_utc": event.get("datetime_utc", None),
            "timezone": event.get("timezone", None),
            "type": type,
            "venue_id": __get_seatgeek_venue_id(event.get("venue", {})),
            "artist_ids": __get_artist_ids(event.get("performers", []), type),
            "min_ticket_price": None,
            "max_ticket_price": None,
            "source": "seatgeek",
            "source_id": event.get("id")
        })

    return processed_events


def process_ticketmaster_events(events: list[dict]) -> list[dict]:
    processed_events = []
    for event in events:
        type = __process_ticketmaster_types(event.get("type", None))
        processed_events.append({
            "name": event.get("name", None),
            "datetime_local": event.get("datetime_local", None),
            "datetime_utc": event.get("datetime_utc", None),
            "timezone": event.get("timezone", None),
            "type": type,
            "venue_id": __get_ticketmaster_venue_id(event.get("venues", [])),
            "artist_ids": __get_artist_ids(event.get("attractions", []), type),
            "min_ticket_price": __get_min_ticket_price(event.get("price_ranges", [])),
            "max_ticket_price": __get_max_ticket_price(event.get("price_ranges", [])),
            "source": "ticketmaster",
            "source_id": event.get("id")
        })

    return processed_events


def process_eventbrite_events(events: list[dict]) -> list[dict]:
    processed_events = []
    for event in events:
        type = __process_eventbrite_types(event.get("type", None))
        
        # Get date range
        start_date = event.get("start_date")
        end_date = event.get("end_date")
        start_time = event.get("start_time")
        timezone_str = event.get("timezone", None)
        
        # Handle multi-day events
        if start_date and end_date and start_date != end_date:
            # Create separate events for each day
            current_date = start_date
            while current_date <= end_date:
                datetime_local, datetime_utc = __convert_eventbrite_datetime_from_fields(current_date, start_time, timezone_str)
                
                processed_events.append({
                    "name": event.get("name", None),
                    "datetime_local": datetime_local,
                    "datetime_utc": datetime_utc,
                    "timezone": timezone_str,
                    "type": type,
                    "venue_id": event.get("venue_id", None),
                    "artist_ids": [],  # Eventbrite events don't have artists
                    "min_ticket_price": event.get("min_ticket_price", None),
                    "max_ticket_price": event.get("max_ticket_price", None),
                    "source": "eventbrite",
                    "source_id": event.get("id") 
                })
                
                current_date += timedelta(days=1)
        else:
            # Single day event
            datetime_local, datetime_utc = __convert_eventbrite_datetime_from_fields(start_date, start_time, timezone_str)
            
            processed_events.append({
                "name": event.get("name", None),
                "datetime_local": datetime_local,
                "datetime_utc": datetime_utc,
                "timezone": timezone_str,
                "type": type,
                "venue_id": event.get("venue_id", None),
                "artist_ids": [],  # Eventbrite events don't have artists
                "min_ticket_price": event.get("min_ticket_price", None),
                "max_ticket_price": event.get("max_ticket_price", None),
                "source": "eventbrite",
                "source_id": event.get("id")
            })

    return processed_events


def __get_best_image_url(ticketmaster_entry: dict) -> Optional[str]:
    """
    Retrieve the highest quality image URL from Ticketmaster images.

    Prioritizes images based on predefined quality indicators and size.

    :param ticketmaster_entry: Dictionary containing Ticketmaster data.
    :return: URL of the best image or None if not available.
    """
    images = ticketmaster_entry.get('images')
    if not images:
        return None

    quality_indicators = [
        'TABLET_LANDSCAPE_LARGE',
        'RETINA_LANDSCAPE',
        'TABLET_LANDSCAPE',
        'RETINA_PORTRAIT'
    ]

    sorted_images = sorted(
        (img for img in images if isinstance(img, dict) and 'url' in img),
        key=lambda x: (
            next(
                (i for i, quality in enumerate(quality_indicators) if quality in x.get('url', '')),
                len(quality_indicators)
            ),
            -x.get('width', 0)
        )
    )

    if sorted_images:
        return sorted_images[0]['url']
    return None


def __process_seatgeek_types(type: Optional[str]) -> str:
    if type in ["animal_sports", "auto_racing", "baseball", "basketball", "boxing", "college_gymnastics",
                "college_lacrosse", "college_softball", "college_track_and_field", "college_volleyball",
                "college_wrestling", "esports", "european_soccer", "extreme_sports", "f1", "fighting",
                "football", "golf", "gymnastics", "hockey", "horse_racing", "indycar", "international_soccer",
                "lacrosse", "lpga", "major_league_lacrosse", "major_league_rugby", "minor_league_baseball",
                "minor_league_hockey", "mlb", "mls", "mma", "monster_truck", "motocross", "nascar", "nascar_cup",
                "national_womens_soccer", "nba", "nba_dleague", "ncaa_baseball", "ncaa_basketball", "ncaa_football",
                "ncaa_hockey", "ncaa_soccer", "ncaa_womens_basketball", "nfl", "nhl", "olympic_sports", "pga",
                "rodeo", "rugby", "soccer", "sports", "super_league_soccer", "tennis", "united_soccer_league",
                "volleyball", "wnba", "womens_college_hockey", "womens_college_lacrosse", "womens_college_soccer",
                "womens_college_volleyball", "wrestling", "wwe", "xfl"]:
        
        return "sports"
    elif type in ["broadway", "broadway_tickets_national", "classical", "classical_opera",
                  "classical_orchestral_instrumental", "classical_vocal", "comedy", "dance",
                  "dance_performance_tour", "film", "opera", "orchestral", "suite", "theater", "vocal"]:
        
        return "arts"
    elif type in ["band", "concert", "music_festival"]:
        
        return "music"
    else:
        return "other"

# Maps Ticketmaster event types to custom types
def __process_ticketmaster_types(type: Optional[str]) -> str:
    if type in ["arts & theatre", "film"]:
        return "arts"
    elif type in ["music"]:
        return "music"
    elif type in ["sports"]:
        return "sports"
    else:
        return "other"

# Maps Eventbrite event types to custom types
def __process_eventbrite_types(type: Optional[str]) -> str:
    if type in ["Music"]:
        return "music"
    elif type in ["Sports & Fitness", "Travel & Outdoor", "Health & Wellness"]:
        return "sports"
    elif type in ["Performing & Visual Arts", "Film, Media & Entertainment", "Fashion & Beauty"]:
        return "arts"
    else:
        return "other"

# Converts SeatGeek venue data from text to json and return the id
def __get_seatgeek_venue_id(venue) -> Optional[str]:
    while not isinstance(venue, dict):
        venue = json.loads(venue)

    # If SeatGeek venue is not available
    if not venue:
        return None
    
    return venue.get("id", None)

# Converts Ticketmaster venue data from text to json and return the id
def __get_ticketmaster_venue_id(venue) -> Optional[str]:
    while not isinstance(venue, list):
        venue = json.loads(venue)
    
    # If Ticketmaster venue is not available
    if len(venue) == 0:
        return None

    return venue[0].get("id", None)

# Returns a simplified list of artist ids
def __get_artist_ids(artists: list[dict] | str, type: str) -> list[str]:
    artist_ids = []

    if type != "music":
        return artist_ids

    while not isinstance(artists, list):
        artists = json.loads(artists)

    for artist in artists:
        artist_ids.append(artist['id'])

    return list(set(artist_ids))

# Calculates the min ticket price
def __get_min_ticket_price(price_ranges: list[dict]) -> Optional[int]:
    if price_ranges:
        return price_ranges[0].get("min", None)
    
    return None

# Calculates the max ticket price
def __get_max_ticket_price(price_ranges: list[dict]) -> Optional[int]:
    if price_ranges:
        return price_ranges[0].get("max", None)
    
    return None