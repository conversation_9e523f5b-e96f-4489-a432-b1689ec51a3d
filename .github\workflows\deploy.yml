name: Build and Deploy Docker Image

on:
  push:
    branches:
      - main

jobs:
  build-push-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Check Out Repo
        uses: actions/checkout@v4

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_IMAGE_PATH }}:${{ secrets.DOCKER_IMAGE_TAG }}

      - name: ZeroTier connection
        uses: zerotier/github-action@v1.0.1
        with:
          network_id: ${{ secrets.ZEROTIER_NETWORK_ID }}
          auth_token: ${{ secrets.ZEROTIER_CENTRAL_TOKEN }}

      - name: Deploy to Host via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_IP }}
          username: ${{ secrets.SSH_USERNAME }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            powershell -Command "& {$Base64Creds = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes('${{ secrets.DOCKER_USERNAME }}:${{ secrets.DOCKER_PASSWORD }}')); $DockerConfig = @{auths = @{'https://index.docker.io/v1/' = @{auth = $Base64Creds}}; credsStore = ''; currentContext = 'default'}; $DockerConfig | ConvertTo-Json | Set-Content -Path $Env:USERPROFILE\.docker\config.json; Write-Output 'Docker credentials configured.'; $container = docker ps -a -q --filter name='${{ secrets.DOCKER_CONTAINER_NAME }}'; if ($container) {docker stop $container; docker rm $container; Write-Output 'Existing containers stopped and removed.'} docker pull ${{ secrets.DOCKER_IMAGE_PATH }}:${{ secrets.DOCKER_IMAGE_TAG }}; docker run -d --name ${{ secrets.DOCKER_CONTAINER_NAME }} --restart always -v ${{ secrets.DOCKER_CONTAINER_NAME }} -e SUPABASE_DBNAME='${{ secrets.SUPABASE_DBNAME }}' -e SUPABASE_HOST='${{ secrets.SUPABASE_HOST }}' -e SUPABASE_PASSWORD='${{ secrets.SUPABASE_PASSWORD }}' -e SUPABASE_PORT='${{ secrets.SUPABASE_PORT }}' -e SUPABASE_USER='${{ secrets.SUPABASE_USER }}' -e SPOTIFY_CLIENT_ID='${{ secrets.SPOTIFY_CLIENT_ID }}' -e SPOTIFY_CLIENT_SECRET='${{ secrets.SPOTIFY_CLIENT_SECRET }}' -e TICKETMASTER_API_KEYS='${{ secrets.TICKETMASTER_API_KEYS }}' -e SEATGEEK_CLIENT_ID='${{ secrets.SEATGEEK_CLIENT_ID }}' -e SEATGEEK_CLIENT_SECRET='${{ secrets.SEATGEEK_CLIENT_SECRET }}' -e SLACK_WEBHOOK='${{ secrets.SLACK_WEBHOOK }}' ${{ secrets.DOCKER_IMAGE_PATH }}:${{ secrets.DOCKER_IMAGE_TAG }}; Write-Output 'Container started successfully.';}" && echo "Logging out..." && docker logout