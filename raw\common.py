from typing import Optional
from datetime import datetime, timedelta, timezone, date, time
from zoneinfo import ZoneInfo

import pickle
import os
import random
import gzip
import json
from datetime import datetime
from typing import Any, List, Dict, Optional
from rich.console import Console
from rich.logging import RichHandler
import logging
import time as time_module

from libs.database.database import Database

# Set up rich logging
FORMAT = "%(message)s"
logging.basicConfig(
    level="INFO",
    format=FORMAT,
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)


class EventbriteQueryGenerator:
    """Generates Eventbrite search queries from public venue data"""
    
    def __init__(self, database: Database, logger: logging.Logger, batch_size: int = 10000):
        """
        Initialize the query generator
        
        Args:
            database: Database instance
            logger: Logger instance
            batch_size: Number of venues to process per batch
        """
        self.database = database
        self.logger = logger
        self.batch_size = batch_size
        
        # Local storage for queries
        self.queries_file = "eventbrite_queries.pkl"
        self.progress_file = "query_generation_progress.pkl"
        
        # Load existing progress if available
        self.progress = self._load_progress()
    
    def _load_progress(self) -> Dict:
        """Load progress from pickle file"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, "rb") as f:
                    return pickle.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to load progress file: {e}")
        return {"last_processed_id": None, "total_processed": 0}
    
    def _save_progress(self):
        """Save progress to pickle file"""
        try:
            with open(self.progress_file, "wb") as f:
                pickle.dump(self.progress, f)
        except Exception as e:
            self.logger.error(f"Failed to save progress: {e}")
    
    def _load_existing_queries(self) -> List[Dict]:
        """Load existing queries from pickle file"""
        if os.path.exists(self.queries_file):
            try:
                with open(self.queries_file, "rb") as f:
                    return pickle.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to load existing queries: {e}")
        return []
    
    def _save_queries(self, queries: List[Dict]):
        """Save queries to pickle file"""
        try:
            with open(self.queries_file, "wb") as f:
                pickle.dump(queries, f)
            self.logger.info(f"Saved {len(queries)} queries to {self.queries_file}")
        except Exception as e:
            self.logger.error(f"Failed to save queries: {e}")
    
    def _generate_search_url(self, venue_name: str) -> str:
        """
        Generate Eventbrite search URL for a venue
        
        Args:
            venue_name: Name of the venue
            
        Returns:
            Eventbrite search URL
        """
        # Clean venue name for URL - convert to lowercase and replace spaces with hyphens
        clean_name = venue_name.strip().lower()
        
        # Remove special characters and replace spaces with hyphens
        import re
        clean_name = re.sub(r'[^a-z0-9\s-]', '', clean_name)  # Keep only letters, numbers, spaces, and hyphens
        clean_name = re.sub(r'\s+', '-', clean_name)  # Replace spaces with hyphens
        clean_name = re.sub(r'-+', '-', clean_name)  # Replace multiple hyphens with single hyphen
        clean_name = clean_name.strip('-')  # Remove leading/trailing hyphens
        
        # Base Eventbrite search URL
        base_url = "https://www.eventbrite.com/d/united-states"
        
        # Add venue name
        search_url = f"{base_url}/{clean_name}/"
        
        return search_url
    
    def _get_venue_batch(self, last_id: Optional[str] = None) -> List[Dict]:
        """
        Get a batch of venues from the public venue table
        
        Args:
            last_id: Last processed venue ID for pagination
            
        Returns:
            List of venue dictionaries
        """
        try:
            venues = self.database.get_venue_batch(last_id, self.batch_size)
            return venues
        except Exception as e:
            self.logger.error(f"Failed to fetch venue batch: {e}")
            return []
    
    def generate_more_queries(self) -> int:
        """
        Generate more Eventbrite queries from public venue data
        
        Returns:
            Number of new queries generated
        """
        # Load existing queries
        existing_queries = self._load_existing_queries()
        existing_venue_ids = {q["venue_id"] for q in existing_queries}
        
        # Get venue batch
        venues = self._get_venue_batch(self.progress["last_processed_id"])
        
        if not venues:
            return 0
        
        # Process venues in this batch
        new_queries = []
        for venue in venues:
            venue_id = venue["id"]
            venue_name = venue["name"]
            
            # Skip if already processed
            if venue_id in existing_venue_ids:
                continue
            
            # Skip if no venue name
            if not venue_name:
                continue
            
            # Generate search URL
            search_url = self._generate_search_url(venue_name)
            
            # Create query object
            query = {
                "venue_id": venue_id,
                "venue_name": venue_name,
                "venue_state": venue.get("state"),
                "eventbrite_search_url": search_url
            }
            
            new_queries.append(query)
            existing_venue_ids.add(venue_id)
        
        # Save new queries
        if new_queries:
            existing_queries.extend(new_queries)
            self._save_queries(existing_queries)
            
            # Update progress
            self.progress["last_processed_id"] = venues[-1]["id"]
            self.progress["total_processed"] += len(venues)
            self._save_progress()
        
        return len(new_queries)
    
    def get_queries(self) -> List[Dict]:
        """
        Get all existing queries
        
        Returns:
            List of query dictionaries
        """
        return self._load_existing_queries()


class EventbriteUtils:
    """Consolidated utility class for Eventbrite data pipeline operations"""
    
    def __init__(self, pool_file: str = "event_id_pool.pkl", database: Database = None):
        """
        Initialize EventbriteUtils
        
        Args:
            pool_file: Path to event ID pool file
            database: Database instance for query generation
        """
        self.console = Console()
        self.log = logging.getLogger("rich")
        
        # Event ID pool
        self.pool_file = pool_file
        self.pool = self._load_pool()
        
        # Default dump directory
        self.default_dump_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
        
        # Query generator
        self.query_generator = None
        if database:
            self.query_generator = EventbriteQueryGenerator(database, self.log)
        
        self.log.info("EventbriteUtils initialized successfully")
    
    def get_queries(self) -> List[Dict]:
        """
        Get Eventbrite queries
        
        Returns:
            List of query dictionaries
        """
        if not self.query_generator:
            self.log.warning("Query generator not initialized - no database provided")
            return []
        
        return self.query_generator.get_queries()
    
    def generate_more_queries(self) -> int:
        """
        Generate more Eventbrite queries when needed
        
        Returns:
            Number of new queries generated
        """
        if not self.query_generator:
            self.log.warning("Query generator not initialized - no database provided")
            return 0
        
        return self.query_generator.generate_more_queries()
    
    def _load_pool(self) -> Dict[str, Dict]:
        """Load event ID pool from file - create if it doesn't exist"""
        if os.path.exists(self.pool_file):
            try:
                with open(self.pool_file, "rb") as f:
                    pool = pickle.load(f)
                self.log.info(f"Loaded existing event pool with {len(pool)} events")
                return pool
            except Exception as e:
                self.log.warning(f"Failed to load existing pool file: {e}, creating new pool")
                pool = {}
        else:
            # Create new pool file
            pool = {}
            self.log.info(f"Creating new event pool file: {self.pool_file}")
        
        # Save the pool file (either new or after failed load)
        try:
            with open(self.pool_file, "wb") as f:
                pickle.dump(pool, f)
            self.log.info("Event pool file initialized successfully")
        except Exception as e:
            self.log.error(f"Failed to create pool file: {e}")
        
        return pool
    
    def _save_pool(self):
        """Save event ID pool to file"""
        try:
            with open(self.pool_file, "wb") as f:
                pickle.dump(self.pool, f)
        except Exception as e:
            self.log.error(f"Failed to save pool file: {e}")
    
    def add_event_ids(self, event_ids: List[str]) -> int:
        """
        Add event IDs to the pool
        
        Args:
            event_ids: List of event IDs to add
            
        Returns:
            Number of new IDs added
        """
        added_count = 0
        for eid in event_ids:
            if eid not in self.pool:
                self.pool[eid] = {"is_scraped": False}
                added_count += 1
        
        if added_count > 0:
            self._save_pool()
        
        return added_count
    
    def get_unscraped_ids(self, limit: int = 20) -> List[str]:
        """
        Get unscraped event IDs from the pool
        
        Args:
            limit: Maximum number of IDs to return
            
        Returns:
            List of unscraped event IDs
        """
        unscraped = [eid for eid, data in self.pool.items() if not data.get("is_scraped", False)]
        if not unscraped:
            return []
        
        selected = random.sample(unscraped, min(limit, len(unscraped)))
        return selected
    
    def mark_scraped(self, event_ids: List[str]) -> int:
        """
        Mark event IDs as scraped
        
        Args:
            event_ids: List of event IDs to mark as scraped
            
        Returns:
            Number of IDs marked as scraped
        """
        updated_count = 0
        for eid in event_ids:
            if eid in self.pool and not self.pool[eid].get("is_scraped", False):
                self.pool[eid]["is_scraped"] = True
                updated_count += 1
        
        if updated_count > 0:
            self._save_pool()
        
        return updated_count
    
    def get_pool_stats(self) -> Dict[str, int]:
        """
        Get statistics about the event ID pool
        
        Returns:
            Dictionary with total, scraped, and unscraped counts
        """
        total = len(self.pool)
        scraped = sum(1 for data in self.pool.values() if data.get("is_scraped", False))
        unscraped = total - scraped
        
        stats = {
            "total": total,
            "scraped": scraped,
            "unscraped": unscraped
        }
        
        return stats
    
    def ensure_dump_dir(self, path: str = None) -> str:
        """Ensure the dump directory exists"""
        dump_dir = path or self.default_dump_dir
        os.makedirs(dump_dir, exist_ok=True)
        return dump_dir
    
    def get_timestamp(self) -> str:
        """Get current timestamp string"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def dump_event_data_gz(self, event_data: Any, event_ids: List[str], output_dir: str = None) -> str:
        """
        Dump event data to compressed JSON file
        
        Args:
            event_data: Event data to dump
            event_ids: List of event IDs for filename
            output_dir: Output directory (optional)
            
        Returns:
            Path to the dumped file
        """
        dump_dir = self.ensure_dump_dir(output_dir)
        timestamp = self.get_timestamp()
        
        # Create filename with event IDs and timestamp
        event_ids_str = "_".join(event_ids[:3])  # Use first 3 IDs for filename
        if len(event_ids) > 3:
            event_ids_str += f"_and_{len(event_ids) - 3}_more"
        
        filename = f"eventbrite_events_{event_ids_str}_{timestamp}.json.gz"
        filepath = os.path.join(dump_dir, filename)
        
        try:
            with gzip.open(filepath, 'wt', encoding='utf-8') as f:
                json.dump(event_data, f, indent=2, ensure_ascii=False)
            
            self.log.info(f"Dumped {len(event_ids)} events to {filepath}")
            return filepath
        except Exception as e:
            self.log.error(f"Failed to dump event data: {e}")
            raise
    
    def load_event_data_gz(self, filepath: str) -> Dict:
        """
        Load event data from compressed JSON file
        
        Args:
            filepath: Path to the compressed JSON file
            
        Returns:
            Loaded event data
        """
        try:
            with gzip.open(filepath, 'rt', encoding='utf-8') as f:
                data = json.load(f)
            
            self.log.info(f"Loaded event data from {filepath}")
            return data
        except Exception as e:
            self.log.error(f"Failed to load event data from {filepath}: {e}")
            raise


def process_seatgeek_events(events):
    """
    Apply schema to the events received from SeatGeek API.
    """
    processed_events = []
    for event in events:
        processed_events.append({
            "id": event.get("id", None),
            "title": event.get("title", None),
            "type": event.get("type", None),
            "datetime_local": event.get("datetime_local", None),
            "datetime_utc": event.get("datetime_utc", None),
            "timezone": event.get("venue", {}).get("timezone", None),
            "venue": event.get("venue", {}),
            "performers": event.get("performers", [])
        })
    
    return processed_events


def process_seatgeek_venue(venue: dict) -> dict:
    """
    Process SeatGeek venue.
    """
    return {
        "id": venue.get("id", None),
        "name": venue.get("name", None),
        "address": venue.get("address", None),
        "city": venue.get("city", None),
        "state": venue.get("state", None),
        "country": venue.get("country", None),
        "postal_code": venue.get("postal_code", None),
        "timezone": venue.get("timezone", None),
        "capacity": venue.get("capacity", None),
        "location": venue.get("location", {})
    }


def process_seatgeek_performer(performer: dict) -> dict:
    """
    Process SeatGeek performer.
    """
    return {
        "id": performer.get("id", None),
        "name": performer.get("name", None),
        "image": performer.get("image", None),
        "genres": performer.get("genres", []),
        "links": performer.get("links", []),
        "spotify_id": __extract_spotify_id_sg(performer.get("links", [])),
    }


def process_ticketmaster_events(events):
    """
    This is a custom function.
    """
    processed_events = []
    for event in events:
        venues = event.get("_embedded", {}).get("venues", [])
        venue_timezone = __get_timezone(venues[0] if venues else None)
        datetime_local, datetime_utc, timezone = __get_datetime(event.get("dates", {}), venue_timezone)

        if __is_test_event(event):
            continue

        processed_events.append({
            "id": event.get("id", None),
            "name": event.get("name", None),
            "type": event.get('classifications', [{}])[0].get('segment', {}).get('name', 'unknown').lower(),
            "datetime_local": datetime_local,
            "datetime_utc": datetime_utc,
            "timezone": timezone,
            "price_ranges": event.get("priceRanges", []),
            "venues": event.get("_embedded", {}).get("venues", []),
            "attractions": event.get("_embedded", {}).get("attractions", [])
        })
    
    return processed_events


def process_ticketmaster_music_performers(attractions: list[dict]) -> list[dict]:
    """
    Process performers from Ticketmaster events that are specifically music-related.
    """
    processed_attractions = []
    if not attractions:
        return processed_attractions
    for attraction in attractions:
        name = attraction.get("name", None)
        spotify_id = __extract_spotify_id_tm(attraction.get('externalLinks', {}))
        if (not name and not spotify_id) or (name and name.lower() in ["no artist", "no artist name"]):
            continue

        processed_attractions.append({
            "id": attraction.get("id", None),
            "name": attraction.get("name"),
            "external_links": attraction.get("externalLinks", {}),
            "aliases": attraction.get("aliases", []),
            "images": attraction.get("images", []),
            "spotify_id": spotify_id
        })

    return processed_attractions


def process_ticketmaster_venues(venues: list[dict]) -> list[dict]:
    """
    Process performers from Ticketmaster events that are specifically music-related.
    """
    processed_venues = []

    venue = venues[0] if venues else None
    if not venue:    
        return processed_venues
    processed_venues.append({
        "id": venue.get("id", None),
        "name": venue.get("name", None),
        "address": venue.get("address", {}).get("line1", None),
        "city": venue.get("city", {}).get("name", None),
        "state": venue.get("state", {}).get("stateCode", None),
        "country": venue.get("country", {}).get("countryCode", None),
        "postal_code": venue.get("postalCode", None),
        "timezone": __get_timezone(venue),
        "location": venue.get("location", {})
    })

    return processed_venues


def get_category_from_tags(tags: List[dict]) -> str:
    """
    Extract Eventbrite category from tags.
    """
    for tag in tags:
        if tag.get("prefix") == "EventbriteCategory":
            return tag.get("display_name", "UNRECOGNIZED")
    return "UNRECOGNIZED"


def process_eventbrite_events(events: list[dict]) -> list[dict]:
    """
    Process Eventbrite events and apply schema.
    """
    processed_events = []
    for event in events:
        if not event:
            continue
            
        # Extract start and end dates/times from the actual API structure
        start_date = event.get("start_date")
        end_date = event.get("end_date")
        start_time = event.get("start_time")
        end_time = event.get("end_time")
        
        # Extract venue info from primary_venue
        venue = event.get("primary_venue", {}) or {}
        venue_id = venue.get("id") if venue else None
        
        # Extract organizer info from primary_organizer
        organizer = event.get("primary_organizer", {}) or {}
        
        # Use venue timezone as event timezone, fallback to event timezone
        event_timezone = event.get("timezone")
        
        # Extract pricing info from ticket_availability
        ticket_availability = event.get("ticket_availability", {}) or {}
        min_price = ticket_availability.get("minimum_ticket_price", {}).get("major_value") if ticket_availability.get("minimum_ticket_price") else None
        max_price = ticket_availability.get("maximum_ticket_price", {}).get("major_value") if ticket_availability.get("maximum_ticket_price") else None
        currency = ticket_availability.get("minimum_ticket_price", {}).get("currency") if ticket_availability.get("minimum_ticket_price") else None
        is_free = ticket_availability.get("is_free", False)
        
        # Get event type from Eventbrite category
        tags = event.get("tags", []) or []
        event_type = get_category_from_tags(tags)
        
        processed_events.append({
            "id": event.get("id"),
            "name": event.get("name"),
            "summary": event.get("summary"),
            "timezone": event_timezone,
            "start_date": start_date,
            "end_date": end_date,
            "start_time": start_time,
            "end_time": end_time,
            "url": event.get("url"),
            "is_online": event.get("is_online_event", False),
            "type": event_type,
            "tags": event.get("tags", []),
            "status": event.get("status"),
            "venue_id": venue_id,
            "organizer_name": organizer.get("name"),
            "organizer_id": organizer.get("id"),
            "organizer_twitter": organizer.get("twitter"),
            "organizer_facebook": organizer.get("facebook"),
            "organizer_website": organizer.get("website_url"),
            "min_ticket_price": min_price,
            "max_ticket_price": max_price,
            "currency": currency,
            "is_free": is_free
        })
    
    return processed_events


def process_eventbrite_venues(venues: list[dict]) -> list[dict]:
    """
    Process Eventbrite venues and apply schema.
    """
    processed_venues = []
    for venue in venues:
        if not venue:
            continue
            
        # Extract location info from address
        address = venue.get("address", {}) or {}
        
        # Extract social links and website
        socials = {}
        venue_url = venue.get("venue_profile_url")
        
        # Check if organizer data is available (added by ETL logic)
        organizer_website = venue.get("organizer_website")
        organizer_facebook = venue.get("organizer_facebook")
        organizer_twitter = venue.get("organizer_twitter")
        
        # Use organizer website if available, otherwise use venue URL
        if organizer_website:
            venue_url = organizer_website
        
        # Build socials from organizer data
        if organizer_facebook:
            socials["facebook"] = organizer_facebook
        if organizer_twitter:
            socials["twitter"] = organizer_twitter
        
        processed_venues.append({
            "id": venue.get("id"),
            "name": venue.get("name"),
            "address": address.get("address_1"),
            "city": address.get("city"),
            "state": address.get("region"),
            "postal_code": address.get("postal_code"),
            "country": address.get("country"),
            "latitude": address.get("latitude"),
            "longitude": address.get("longitude"),
            "timezone": venue.get("timezone"),
            "url": venue_url,
            "socials": socials if socials else None
        })
    
    return processed_venues


#--------------songkick_Start---------------
def process_songkick_venues(venues: List[Dict]) -> List[Dict]:
    """
    Process raw Songkick venue data into standardized format.
    
    Args:
        venues: List of raw venue dictionaries
        
    Returns:
        List of processed venue dictionaries
    """
    processed_venues = []
    
    for venue in venues:
        if not venue or not venue.get('name'):
            continue
        
        # Extract social media links as JSONB
        social_links = venue.get('social_media_links', [])
        socials = {}
        
        if social_links:
            for link in social_links:
                if 'facebook.com' in link.lower():
                    socials['facebook'] = link
                elif 'twitter.com' in link.lower():
                    socials['twitter'] = link
                elif 'instagram.com' in link.lower():
                    socials['instagram'] = link
                elif 'youtube.com' in link.lower():
                    socials['youtube'] = link
                elif 'linkedin.com' in link.lower():
                    socials['linkedin'] = link
                else:
                    # Generic social link
                    if 'other' not in socials:
                        socials['other'] = []
                    socials['other'].append(link)
        
        processed_venue = {
            'id': venue.get('id'),
            'name': venue.get('name'),
            'address': venue.get('address'),
            'city': venue.get('city'),
            'state': venue.get('state'),
            'country': venue.get('country'),
            'postal_code': venue.get('postal_code'),
            'latitude': venue.get('latitude'),
            'longitude': venue.get('longitude'),
            'website_url': venue.get('website_url'),
            'socials': socials if socials else None,
            'source_url': venue.get('source_url'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        processed_venues.append(processed_venue)
    
    return processed_venues


class SongkickUtils:
    """Utility class for managing Songkick venue URLs and processing."""
    
    def __init__(self, database: Database, logger: logging.Logger = None):
        """
        Initialize SongkickUtils.
        
        Args:
            database: Database instance
            logger: Logger instance
        """
        self.database = database
        self.logger = logger
        self.venue_urls_file = "songkick_venue_urls.json"
        self.processed_urls_file = "songkick_processed_urls.json"
    
    def load_venue_urls(self) -> List[str]:
        """Load venue URLs from file or database."""
        try:
            # First try to load from file
            if os.path.exists(self.venue_urls_file):
                with open(self.venue_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    urls = data.get('urls', [])
                    if self.logger:
                        self.logger.info(f"Loaded {len(urls)} venue URLs from file")
                    return urls
            
            # If no file exists, try to get from database
            try:
                urls = self.database.get_songkick_venue_urls()
                if urls:
                    # Save to file for future use
                    self.save_venue_urls(urls)
                    if self.logger:
                        self.logger.info(f"Loaded {len(urls)} venue URLs from database")
                    return urls
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"Could not load URLs from database: {e}")
            
            if self.logger:
                self.logger.warning("No venue URLs found in file or database")
            return []
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error loading venue URLs: {e}")
            return []
    
    def save_venue_urls(self, urls: List[str]) -> None:
        """Save venue URLs to file."""
        try:
            data = {
                "total_urls": len(urls),
                "extracted_date": datetime.now().isoformat(),
                "urls": urls
            }
            
            with open(self.venue_urls_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            if self.logger:
                self.logger.info(f"Saved {len(urls)} venue URLs to file")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error saving venue URLs: {e}")
    
    def load_processed_urls(self) -> set:
        """Load processed URLs from file."""
        try:
            if os.path.exists(self.processed_urls_file):
                with open(self.processed_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    processed = set(data.get('processed_urls', []))
                    if self.logger:
                        self.logger.info(f"Loaded {len(processed)} processed URLs from file")
                    return processed
            
            return set()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error loading processed URLs: {e}")
            return set()
    
    def save_processed_urls(self, processed_urls: set) -> None:
        """Save processed URLs to file."""
        try:
            data = {
                "total_processed": len(processed_urls),
                "last_updated": datetime.now().isoformat(),
                "processed_urls": list(processed_urls)
            }
            
            with open(self.processed_urls_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            if self.logger:
                self.logger.info(f"Saved {len(processed_urls)} processed URLs to file")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error saving processed URLs: {e}")
    
    def get_unprocessed_urls(self, limit: int = 50) -> List[str]:
        """Get unprocessed venue URLs."""
        try:
            all_urls = self.load_venue_urls()
            processed_urls = self.load_processed_urls()
            
            unprocessed = [url for url in all_urls if url not in processed_urls]
            
            if self.logger:
                self.logger.info(f"Found {len(unprocessed)} unprocessed URLs out of {len(all_urls)} total")
            
            return unprocessed[:limit]
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting unprocessed URLs: {e}")
            return []
    
    def mark_urls_processed(self, urls: List[str]) -> None:
        """Mark URLs as processed."""
        try:
            processed_urls = self.load_processed_urls()
            processed_urls.update(urls)
            self.save_processed_urls(processed_urls)
            
            if self.logger:
                self.logger.info(f"Marked {len(urls)} URLs as processed")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error marking URLs as processed: {e}")
    
    def get_processing_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        try:
            all_urls = self.load_venue_urls()
            processed_urls = self.load_processed_urls()
            
            total = len(all_urls)
            processed = len(processed_urls)
            remaining = total - processed
            
            return {
                "total": total,
                "processed": processed,
                "remaining": remaining,
                "completion_percentage": round((processed / total) * 100, 2) if total > 0 else 0
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting processing stats: {e}")
            return {"total": 0, "processed": 0, "remaining": 0, "completion_percentage": 0}
#--------------songkick_End---------------


def remove_duplicates_by_id(dicts_list):
    seen_ids = set()
    unique_dicts = []
    for d in dicts_list:
        if d['id'] not in seen_ids:
            unique_dicts.append(d)
            seen_ids.add(d['id'])
    return unique_dicts


def get_data_date(last_processed_date: datetime):
    if ((last_processed_date.date() - datetime.now(timezone.utc).date()).days > 365
        or last_processed_date.date() == date(1900, 1, 1)):
        return datetime.now(timezone.utc).date()
        
    return last_processed_date.date() + timedelta(days=1)


def to_datetime_utc(date_obj: date) -> datetime:
    return datetime.combine(date_obj, time(tzinfo=timezone.utc))


def __extract_spotify_id_sg(links: dict) -> Optional[str]:
    """
    Extract Spotify artist ID from external links, ignoring non-artist IDs.
    """
    for link in links:
        if link.get('provider') == 'spotify':
            return link.get('id').split(':')[-1]


def __get_datetime(dates: dict, venue_timezone: Optional[str]) -> tuple[Optional[str], Optional[str], Optional[str]]:
    start_date = dates.get("start", {})

    local_date = start_date.get("localDate", None)
    local_time = start_date.get("localTime", "00:00:00")

    datetime_local = None
    if local_date and local_time:
        datetime_local = datetime.strptime(f"{local_date} {local_time}", "%Y-%m-%d %H:%M:%S")

    datetime_utc = start_date.get('dateTime', None)
    timezone = dates.get("timezone", None) or venue_timezone

    if datetime_utc:
        datetime_utc = datetime.strptime(datetime_utc, "%Y-%m-%dT%H:%M:%SZ")
    
    if datetime_utc is None and datetime_local and timezone:
        local_zone = ZoneInfo(timezone)
        datetime_utc = datetime_local.replace(tzinfo=ZoneInfo("UTC")).astimezone(local_zone)

    datetime_local_str = datetime_local.strftime("%Y-%m-%d %H:%M:%S") if datetime_local else None
    datetime_utc_str = datetime_utc.strftime("%Y-%m-%d %H:%M:%S%z") if datetime_utc else None

    return datetime_local_str, datetime_utc_str, timezone


def __get_timezone(venue: Optional[dict]) -> Optional[str]:
    """
    Get the timezone of a venue.
    """
    if not venue:
        return None
    timezone = venue.get("timezone", None)

    if timezone and timezone.lower() == "no timezone":
        timezone = None

    return timezone


def __is_test_event(event: dict) -> bool:
    if event.get("test", True):
        return True
    
    embedded = event.get("_embedded", {})
    
    return any(venue.get("test", True) for venue in embedded.get("venues", [])) or \
           any(attraction.get("test", True) for attraction in embedded.get("attractions", []))


def __extract_spotify_id_tm(external_links: dict) -> Optional[str]:
    """
    Extract Spotify artist ID from external links, ignoring non-artist IDs.
    """
    spotify_links = external_links.get('spotify', [])
    #print(spotify_links)  # Debug print to check the structure of links
    if spotify_links:
        for link in spotify_links:
            spotify_url = link.get('url', '')
            # Check if the URL is for an artist
            if '/artist/' in spotify_url:
                # Split the URL by '/' and attempt to extract the artist ID
                parts = spotify_url.split('/')
                if 'artist' in parts:
                    artist_index = parts.index('artist') + 1
                    if artist_index < len(parts):
                        artist_id = parts[artist_index].split('?')[0]  # Remove any query parameters
                        return artist_id
    return None